# Component Conversion Example

This example shows how to convert an existing component to use the new theme system.

## Before: Original Component

Here's the original `Cta13.jsx` component from the forestforward section:

```jsx
"use client";

import { Button } from "@relume_io/relume-ui";
import React from "react";

export function Cta13() {
  return (
    <section id="relume" className="relative px-[5%] py-16 md:py-24 lg:py-28">
      <div className="container grid grid-rows-1 items-start gap-y-5 md:grid-cols-2 md:gap-x-12 md:gap-y-8 lg:gap-x-20 lg:gap-y-16">
        <div>
          <h1 className="text-5xl font-bold md:text-7xl lg:text-8xl">
            Nood aan boscompensatie?
          </h1>
        </div>
        <div>
          <p className="md:text-md">
            We zijn het erover eens dat bossen heel belangrijk zijn...
          </p>
          <div className="mt-6 flex flex-wrap gap-4 md:mt-8">
            <Button title="<PERSON><PERSON> meer"><PERSON><PERSON> meer</Button>
            <Button title="Contacteer ons" variant="secondary">
              Contacteer ons
            </Button>
          </div>
        </div>
      </div>
    </section>
  );
}
```

## After: Theme-Aware Component

Here's how to convert it to use theme-aware colors:

```jsx
"use client";

import { ThemedButton } from "../../_components/examples/ThemedButton";
import React from "react";

export function Cta13() {
  return (
    <section 
      id="relume" 
      className="relative px-[5%] py-16 md:py-24 lg:py-28 bg-background-primary"
    >
      <div className="container grid grid-rows-1 items-start gap-y-5 md:grid-cols-2 md:gap-x-12 md:gap-y-8 lg:gap-x-20 lg:gap-y-16">
        <div>
          <h1 className="text-5xl font-bold md:text-7xl lg:text-8xl text-text-primary">
            Nood aan boscompensatie?
          </h1>
        </div>
        <div>
          <p className="md:text-md text-text-secondary">
            We zijn het erover eens dat bossen heel belangrijk zijn...
          </p>
          <div className="mt-6 flex flex-wrap gap-4 md:mt-8">
            <ThemedButton variant="primary">Lees meer</ThemedButton>
            <ThemedButton variant="secondary">Contacteer ons</ThemedButton>
          </div>
        </div>
      </div>
    </section>
  );
}
```

## Changes Made

### 1. Background Color
- **Added**: `bg-background-primary` to the section
- **Result**: Section background will adapt to each theme's primary background color

### 2. Text Colors
- **Added**: `text-text-primary` to the heading
- **Added**: `text-text-secondary` to the paragraph
- **Result**: Text colors will adapt to each theme's color scheme

### 3. Buttons
- **Replaced**: `Button` from Relume UI with `ThemedButton`
- **Result**: Buttons will use theme-aware colors instead of fixed colors

## Theme Results

With these changes, the component will look different in each theme:

### Forest Forward Theme (Green)
- Background: Light green (`#f0fdf4`)
- Heading: Dark green (`#15803d`)
- Text: Medium green (`#166534`)
- Primary button: Green (`#22c55e`)

### Lagom Theme (Orange)
- Background: Light orange (`#fff7ed`)
- Heading: Dark orange (`#c2410c`)
- Text: Medium orange (`#ea580c`)
- Primary button: Orange (`#f97316`)

### Story Forward Theme (Purple)
- Background: Light purple (`#faf5ff`)
- Heading: Dark purple (`#7c3aed`)
- Text: Medium purple (`#8b5cf6`)
- Primary button: Purple (`#a855f7`)

## Alternative: Using Custom Button Styles

If you prefer to keep using the existing Button component but make it theme-aware:

```jsx
<Button 
  title="Lees meer"
  className="bg-link text-text-alternative hover:bg-link-primary"
>
  Lees meer
</Button>
<Button 
  title="Contacteer ons" 
  variant="secondary"
  className="bg-background-secondary text-text-primary border-border-primary hover:bg-background-tertiary"
>
  Contacteer ons
</Button>
```

## Testing the Changes

1. Visit the forestforward section - should show green theme
2. Visit the lagom section - should show orange theme  
3. Visit the storyforward section - should show purple theme
4. Visit the homepage - should show blue theme

## Best Practices Applied

✅ **Used semantic color names** (`text-primary`, `bg-background-primary`) instead of specific colors  
✅ **Maintained design consistency** (spacing, typography, layout unchanged)  
✅ **Made colors theme-aware** while keeping static elements static  
✅ **Preserved component functionality** while enhancing visual adaptability
