'use client';

import { useState } from 'react';
import { ThemedButton } from '../_components/examples/ThemedButton';
import { ThemedCard } from '../_components/examples/ThemedCard';
import { ThemedNavigation } from '../_components/examples/ThemedNavigation';
import { THEMES } from '../utils/theme';

const navItems = [
  { label: 'Home', href: '/' },
  { label: 'About', href: '/about' },
  { label: 'Services', href: '/services' },
  { label: 'Contact', href: '/contact' },
];

export default function ThemeDemo() {
  const [currentTheme, setCurrentTheme] = useState(THEMES.HOMEPAGE);

  return (
    <div className={currentTheme}>
      <div className="min-h-screen bg-background">
        {/* Theme Selector */}
        <div className="bg-background-primary border-b border-border p-4">
          <div className="container mx-auto">
            <h1 className="text-2xl font-bold text-text-primary mb-4">
              Multi-Theme Demo
            </h1>
            <div className="flex flex-wrap gap-2">
              {Object.entries(THEMES).map(([name, themeClass]) => (
                <button
                  key={name}
                  onClick={() => setCurrentTheme(themeClass)}
                  className={`px-4 py-2 rounded-md text-sm font-medium transition-colors ${
                    currentTheme === themeClass
                      ? 'bg-link text-text-alternative'
                      : 'bg-background-secondary text-text-primary hover:bg-background-tertiary'
                  }`}
                >
                  {name.charAt(0) + name.slice(1).toLowerCase().replace('_', ' ')}
                </button>
              ))}
            </div>
          </div>
        </div>

        {/* Navigation Demo */}
        <ThemedNavigation items={navItems} />

        {/* Content Demo */}
        <div className="container mx-auto px-4 py-8">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {/* Card Examples */}
            <ThemedCard 
              title="Default Card" 
              subtitle="This is a default themed card"
            >
              <p className="text-text-secondary mb-4">
                This card uses the default theme colors and will adapt to the current theme.
              </p>
              <ThemedButton variant="primary">Primary Action</ThemedButton>
            </ThemedCard>

            <ThemedCard 
              title="Primary Card" 
              subtitle="This is a primary themed card"
              variant="primary"
            >
              <p className="text-text-secondary mb-4">
                This card uses primary background colors from the current theme.
              </p>
              <ThemedButton variant="secondary">Secondary Action</ThemedButton>
            </ThemedCard>

            <ThemedCard 
              title="Alternative Card" 
              subtitle="This is an alternative themed card"
              variant="alternative"
            >
              <p className="text-text-alternative mb-4">
                This card uses alternative colors with light text.
              </p>
              <ThemedButton variant="outline" className="border-text-alternative text-text-alternative hover:bg-text-alternative hover:text-background">
                Outline Action
              </ThemedButton>
            </ThemedCard>
          </div>

          {/* Button Examples */}
          <div className="mt-12">
            <h2 className="text-xl font-semibold text-text-primary mb-6">
              Button Variants
            </h2>
            <div className="flex flex-wrap gap-4">
              <ThemedButton variant="primary">Primary</ThemedButton>
              <ThemedButton variant="secondary">Secondary</ThemedButton>
              <ThemedButton variant="outline">Outline</ThemedButton>
              <ThemedButton variant="ghost">Ghost</ThemedButton>
            </div>
          </div>

          {/* Typography Examples */}
          <div className="mt-12">
            <h2 className="text-xl font-semibold text-text-primary mb-6">
              Typography
            </h2>
            <div className="space-y-4">
              <h1 className="text-4xl font-bold text-text-primary">
                Heading 1 - Primary Text
              </h1>
              <h2 className="text-2xl font-semibold text-text-secondary">
                Heading 2 - Secondary Text
              </h2>
              <p className="text-text">
                Regular paragraph text using the default text color.
              </p>
              <a href="#" className="text-link hover:text-link-primary">
                This is a themed link
              </a>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
